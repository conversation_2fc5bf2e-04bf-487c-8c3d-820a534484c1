# Personel Yetkinlik Değerlendirme Sistemi - Teknik Dokümantasyon

## İçindekiler
1. [<PERSON><PERSON><PERSON>l <PERSON>](#1-sistem-genel-bakış)
2. [<PERSON><PERSON> (Business Workflows)](#2-iş-akışları-business-workflows)
3. [<PERSON>t<PERSON><PERSON><PERSON> (Logical Architecture)](#3-mantıksal-yapı-logical-architecture)
4. [Veri Modeli](#4-veri-modeli)
5. [API Endpoint'leri](#5-api-endpointleri)
6. [Performans ve Optimizasyon](#6-performans-ve-optimizasyon)
7. [<PERSON><PERSON><PERSON><PERSON>](#7-güven<PERSON>)

---

## 1. Sistem Genel Bakış

### 1.1 Amaç ve Kapsam

Personel Yetkinlik Değerlendirme Sistemi, akademik personelin çeşitli yetkinlik alanlarındaki performansın<PERSON> de<PERSON>dirmek, izlemek ve raporlamak için geliştirilmiş kapsamlı bir sistemdir. <PERSON><PERSON><PERSON>, objektif değerlendirme kriterleri kull<PERSON>rak personelin güçlü ve gelişim alanlarını belirler.

**Ana Hedefler:**
- Personel yetkinliklerinin sistematik değerlendirilmesi
- Objektif ve şeffaf değerlendirme süreçleri
- Performans takibi ve trend analizi
- Kapsamlı raporlama ve analiz imkanları
- Personel gelişim planlaması desteği

### 1.2 Sistemin Organizasyon İçindeki Rolü

Sistem, akademik kurumların insan kaynakları yönetiminde kritik bir rol oynar:

- **Stratejik Planlama**: Kurumsal hedeflere uygun personel gelişim stratejileri
- **Performans Yönetimi**: Objektif performans değerlendirme ve takip
- **Karar Destek**: Terfi, ödül ve gelişim kararlarında veri desteği
- **Kalite Güvence**: Akademik kalite standartlarının sürdürülmesi
- **Sürekli İyileştirme**: Personel gelişim alanlarının belirlenmesi

### 1.3 Ana Kullanıcı Grupları ve Rolleri

#### 1.3.1 Sistem Yöneticileri (Admin)
- **Yetkiler**: Tam sistem erişimi, konfigürasyon yönetimi
- **Sorumluluklar**: Sistem bakımı, kullanıcı yönetimi, güvenlik
- **Kullanım Senaryoları**: Sistem konfigürasyonu, hata giderme, raporlama

#### 1.3.2 Bölüm Yöneticileri (Department Managers)
- **Yetkiler**: Bölüm personeli değerlendirme, raporlama
- **Sorumluluklar**: Değerlendirme süreçlerinin yönetimi, onay işlemleri
- **Kullanım Senaryoları**: Personel değerlendirme, performans takibi

#### 1.3.3 Değerlendiriciler (Evaluators)
- **Yetkiler**: Değerlendirme formu doldurma, güncelleme
- **Sorumluluklar**: Objektif ve zamanında değerlendirme yapma
- **Kullanım Senaryoları**: Yetkinlik değerlendirmesi, form doldurma

#### 1.3.4 Raporlama Kullanıcıları (Report Viewers)
- **Yetkiler**: Raporları görüntüleme, analiz yapma
- **Sorumluluklar**: Veri analizi, karar destek raporları hazırlama
- **Kullanım Senaryoları**: Trend analizi, karşılaştırmalı raporlar

---

## 2. İş Akışları (Business Workflows)

### 2.1 Yetkinlik Değerlendirme Süreci

#### 2.1.1 Değerlendirme Başlatma
```
Başlangıç → Personel Seçimi → Dönem Belirleme → Değerlendirici Atama → Form Oluşturma
```

**Adımlar:**
1. **Personel Seçimi**: Değerlendirilecek personelin belirlenmesi
2. **Dönem Belirleme**: Değerlendirme döneminin tanımlanması
3. **Değerlendirici Atama**: Uygun değerlendiricinin seçilmesi
4. **Form Oluşturma**: Değerlendirme formunun hazırlanması

#### 2.1.2 Değerlendirme Süreci
```
Form Doldurma → Validasyon → Kaydetme → Gözden Geçirme → Onay
```

**Detaylı Akış:**
1. **Form Doldurma**: Değerlendirici tarafından yetkinlik skorlarının girilmesi
2. **Otomatik Validasyon**: Sistem tarafından veri doğrulama
3. **Kaydetme**: Değerlendirme verilerinin veritabanına kaydedilmesi
4. **Gözden Geçirme**: Bölüm yöneticisi tarafından kontrol
5. **Onay**: Final onay ve değerlendirmenin tamamlanması

### 2.2 Değerlendirme Formu Oluşturma ve Onay Süreci

#### 2.2.1 Form Oluşturma Akışı
```
Şablon Seçimi → Kişiselleştirme → Yetkinlik Alanları Belirleme → Form Hazırlama
```

#### 2.2.2 Onay Süreci
```
Form Gönderimi → Ön Kontrol → Bölüm Onayı → Final Onay → Arşivleme
```

**Onay Seviyeleri:**
- **Seviye 1**: Otomatik validasyon kontrolü
- **Seviye 2**: Bölüm yöneticisi onayı
- **Seviye 3**: İnsan kaynakları onayı (gerektiğinde)

### 2.3 Puanlama ve Değerlendirme Metodolojisi

#### 2.3.1 Yetkinlik Skorlama Sistemi
- **Skala**: 1-5 arası puanlama sistemi
- **Kriterler**: Önceden tanımlanmış yetkinlik kriterleri
- **Ağırlıklandırma**: Yetkinlik alanlarına göre ağırlık katsayıları

#### 2.3.2 Hesaplama Metodolojisi
```
Bireysel Skorlar → Ağırlıklı Ortalama → Alan Skorları → Genel Skor
```

**Hesaplama Formülü:**
- Alan Skoru = Σ(Yetkinlik Skoru × Ağırlık) / Toplam Ağırlık
- Genel Skor = Σ(Alan Skoru × Alan Ağırlığı) / Toplam Alan Ağırlığı

### 2.4 Rapor Oluşturma ve Analiz Süreçleri

#### 2.4.1 Rapor Türleri
- **Bireysel Raporlar**: Tek personel odaklı detaylı analiz
- **Bölüm Raporları**: Bölüm bazında karşılaştırmalı analiz
- **Fakülte Raporları**: Geniş kapsamlı kurumsal analiz
- **Trend Raporları**: Zaman bazlı performans analizi

#### 2.4.2 Rapor Oluşturma Akışı
```
Rapor Talebi → Parametre Belirleme → Veri Toplama → Analiz → Rapor Üretimi
```

### 2.5 Hata Durumları ve İstisna Yönetimi

#### 2.5.1 Hata Kategorileri
- **Veri Doğrulama Hataları**: Geçersiz skor girişleri
- **Yetkilendirme Hataları**: Yetkisiz erişim denemeleri
- **Sistem Hataları**: Teknik altyapı sorunları
- **İş Kuralı İhlalleri**: Duplicate değerlendirme girişleri

#### 2.5.2 Hata Yönetim Stratejisi
```
Hata Tespit → Loglama → Kullanıcı Bilgilendirme → Otomatik Düzeltme/Manuel Müdahale
```

---

## 3. Mantıksal Yapı (Logical Architecture)

### 3.1 Sistem Bileşenleri Arasındaki İlişkiler

#### 3.1.1 Katmanlı Mimari Yapısı
```
Presentation Layer (Controllers) ↔ Business Layer (Managers) ↔ Data Layer (Stores)
```

#### 3.1.2 Bileşen İlişki Diyagramı
```
StaffCompetencyController → StaffCompetencyManager → StaffCompetencyStore → Database
                        ↓                        ↓                      ↓
                   Authorization            Cache Service         Entity Framework
```

### 3.2 Veri Akışı Diyagramları

#### 3.2.1 Değerlendirme Oluşturma Veri Akışı
```
Client Request → Controller → Manager → Validation → Store → Database
                                    ↓
                              Cache Update ← Cache Service
```

#### 3.2.2 Rapor Oluşturma Veri Akışı
```
Report Request → Controller → Manager → Cache Check → Store → Data Aggregation → Report Generation
```

### 3.3 Controller-Manager-Store Katman Mimarisi

#### 3.3.1 Controller Katmanı (Presentation Layer)
- **Sorumluluk**: HTTP isteklerini karşılama, response oluşturma
- **Bileşenler**: StaffCompetencyController
- **Özellikler**: Authorization, validation, error handling

#### 3.3.2 Manager Katmanı (Business Layer)
- **Sorumluluk**: İş mantığı, orchestration, validation
- **Bileşenler**: StaffCompetencyManager
- **Özellikler**: Business rules, data transformation, caching

#### 3.3.3 Store Katmanı (Data Layer)
- **Sorumluluk**: Veri erişimi, CRUD operasyonları
- **Bileşenler**: StaffCompetencyStore
- **Özellikler**: Database operations, query optimization

### 3.4 Cache Stratejisi ve Veri Yönetimi

#### 3.4.1 Cache Katmanları
- **L1 Cache**: Memory cache (kısa süreli)
- **L2 Cache**: Redis distributed cache (orta süreli)
- **L3 Cache**: Database query cache (uzun süreli)

#### 3.4.2 Cache Stratejileri
- **Staff Evaluations**: 30 dakika TTL
- **Statistics**: 60 dakika TTL
- **Reports**: 120 dakika TTL
- **Static Data**: 24 saat TTL

### 3.5 Authorization ve Güvenlik Modeli

#### 3.5.1 Yetkilendirme Katmanları
```
User Authentication → Role-Based Access → Policy-Based Authorization → Resource Access
```

#### 3.5.2 Güvenlik Politikaları
- **RequireAdminRole**: Sistem yöneticisi yetkisi
- **InputDepartmentData**: Veri girişi yetkisi
- **ViewReports**: Rapor görüntüleme yetkisi
- **ManageCriteria**: Kriter yönetimi yetkisi

---

## 4. Veri Modeli

### 4.1 Entity İlişkileri ve Veritabanı Şeması

#### 4.1.1 Ana Entity'ler
- **StaffCompetencyEvaluationEntity**: Ana değerlendirme entity'si
- **CompetencyRatingEntity**: Yetkinlik puanlama entity'si
- **StaffCompetencyDefinitionEntity**: Yetkinlik tanım entity'si

#### 4.1.2 Entity İlişki Diyagramı
```
StaffCompetencyEvaluationEntity (1) ←→ (N) CompetencyRatingEntity
CompetencyRatingEntity (N) ←→ (1) StaffCompetencyDefinitionEntity
```

### 4.2 DTO Yapıları ve Veri Transferi

#### 4.2.1 Ana DTO Sınıfları
- **StaffCompetencyDto**: Genel değerlendirme DTO'su
- **StaffCompetencyCreateDto**: Oluşturma DTO'su
- **StaffCompetencyUpdateDto**: Güncelleme DTO'su
- **StaffCompetencyFilterDto**: Filtreleme DTO'su

#### 4.2.2 Veri Transfer Akışı
```
Entity ←→ DTO ←→ JSON ←→ Client
```

### 4.3 Validation Kuralları ve İş Mantığı Kısıtlamaları

#### 4.3.1 Veri Doğrulama Kuralları
- **Skor Aralığı**: 1-5 arası değerler
- **Zorunlu Alanlar**: StaffId, CompetencyId, Rating
- **Unique Constraints**: Staff + Period + Evaluator kombinasyonu
- **String Uzunlukları**: Tanımlanmış maksimum uzunluklar

#### 4.3.2 İş Mantığı Kısıtlamaları
- **Duplicate Prevention**: Aynı dönem için tekrar değerlendirme engelleme
- **Status Validation**: Geçerli durum geçişleri
- **Date Validation**: Mantıklı tarih aralıkları
- **Authorization Checks**: Yetki bazlı veri erişimi

---

## 5. API Endpoint'leri

### 5.1 CRUD Operasyonları

#### 5.1.1 Değerlendirme Oluşturma
- **Endpoint**: `POST /StaffCompetency/CreateStaffCompetency`
- **Authorization**: `InputDepartmentData`
- **İşlev**: Yeni personel yetkinlik değerlendirmesi oluşturma
- **Request**: StaffCompetencyCreateDto
- **Response**: StaffCompetencyDto

#### 5.1.2 Değerlendirme Güncelleme
- **Endpoint**: `PUT /StaffCompetency/UpdateStaffCompetency`
- **Authorization**: `InputDepartmentData`
- **İşlev**: Mevcut değerlendirmeyi güncelleme
- **Request**: StaffCompetencyUpdateDto
- **Response**: Boolean success

#### 5.1.3 Değerlendirme Silme
- **Endpoint**: `DELETE /StaffCompetency/DeleteStaffCompetency/{id}`
- **Authorization**: `InputDepartmentData`
- **İşlev**: Değerlendirmeyi soft delete
- **Response**: Boolean success

### 5.2 Sorgulama Operasyonları

#### 5.2.1 Filtreleme ve Listeleme
- **Endpoint**: `POST /StaffCompetency/GetStaffCompetencies`
- **Authorization**: `ViewReports`
- **İşlev**: Sayfalanmış ve filtrelenmiş değerlendirme listesi
- **Request**: PagedListCo<StaffCompetencyFilterDto>
- **Response**: PagedListDto<StaffCompetencyDto>

#### 5.2.2 Tekil Değerlendirme Getirme
- **Endpoint**: `GET /StaffCompetency/GetStaffCompetency/{id}`
- **Authorization**: `ViewReports`
- **İşlev**: Belirli bir değerlendirmenin detayları
- **Response**: StaffCompetencyDto

### 5.3 Analiz ve Raporlama

#### 5.3.1 Dashboard Verileri
- **Endpoint**: `GET /StaffCompetency/GetStaffCompetencyDashboard`
- **Authorization**: `ViewReports`
- **İşlev**: Dashboard için özet veriler
- **Parameters**: departmentId, period
- **Response**: StaffCompetencyDashboardDto

#### 5.3.2 Karşılaştırma Analizi
- **Endpoint**: `POST /StaffCompetency/CompareStaffCompetencies`
- **Authorization**: `ViewReports`
- **İşlev**: Çoklu personel karşılaştırması
- **Request**: StaffCompetencyComparisonRequestDto
- **Response**: List<StaffCompetencyComparisonDto>

#### 5.3.3 Rapor Oluşturma
- **Endpoint**: `GET /StaffCompetency/GenerateCompetencyReport`
- **Authorization**: `ViewReports`
- **İşlev**: Detaylı rapor oluşturma
- **Parameters**: reportType, scopeId, period
- **Response**: StaffCompetencyReportDto

### 5.4 Hata Kodları ve Mesajları

#### 5.4.1 HTTP Status Kodları
- **200 OK**: Başarılı operasyon
- **400 Bad Request**: Geçersiz istek parametreleri
- **401 Unauthorized**: Kimlik doğrulama hatası
- **403 Forbidden**: Yetkilendirme hatası
- **404 Not Found**: Kaynak bulunamadı
- **500 Internal Server Error**: Sistem hatası

#### 5.4.2 Özel Hata Mesajları
- **"Bu personel için bu dönemde zaten bir değerlendirme mevcut"**: Duplicate değerlendirme
- **"Geçersiz yetkinlik puanı"**: Skor validasyon hatası
- **"Yetkilendirme hatası"**: Authorization failure
- **"Kaynak bulunamadı"**: Entity not found

---

## 6. Performans ve Optimizasyon

### 6.1 Cache Mekanizması ve Stratejileri

#### 6.1.1 Cache Katmanları
- **Memory Cache**: Sık erişilen veriler için
- **Redis Distributed Cache**: Ölçeklenebilir cache çözümü
- **Database Query Cache**: Karmaşık sorgu sonuçları için

#### 6.1.2 Cache Stratejileri
- **Write-Through**: Veri yazımında cache güncelleme
- **Cache-Aside**: İhtiyaç durumunda cache doldurma
- **TTL-Based Expiration**: Zaman bazlı cache temizleme

### 6.2 Database İndeksleri ve Sorgu Optimizasyonları

#### 6.2.1 Kritik İndeksler
- **Primary Keys**: Tüm entity'lerde otomatik
- **Foreign Keys**: İlişkisel sorgular için
- **Composite Indexes**: Çoklu alan sorguları için
- **Filtered Indexes**: Koşullu sorgular için

#### 6.2.2 Sorgu Optimizasyon Teknikleri
- **Eager Loading**: İlişkili verilerin tek sorguda yüklenmesi
- **Projection**: Sadece gerekli alanların seçilmesi
- **Pagination**: Büyük veri setlerinin sayfalanması
- **Query Hints**: Veritabanı optimizer'a yönlendirme

### 6.3 Logging ve Monitoring Yaklaşımları

#### 6.3.1 Log Seviyeleri
- **Information**: Normal operasyon logları
- **Warning**: Dikkat gerektiren durumlar
- **Error**: Hata durumları
- **Critical**: Sistem kritik hataları

#### 6.3.2 Monitoring Metrikleri
- **Response Time**: API yanıt süreleri
- **Throughput**: İstek işleme kapasitesi
- **Error Rate**: Hata oranları
- **Cache Hit Ratio**: Cache başarı oranları

---

## 7. Güvenlik

### 7.1 Yetkilendirme Politikaları

#### 7.1.1 Role-Based Access Control (RBAC)
- **Admin**: Tam sistem erişimi
- **Manager**: Bölüm bazlı yönetim
- **Evaluator**: Değerlendirme yetkisi
- **Viewer**: Sadece görüntüleme

#### 7.1.2 Policy-Based Authorization
- **Claim-Based**: Kullanıcı claim'lerine dayalı yetkilendirme
- **Resource-Based**: Kaynak bazlı erişim kontrolü
- **Context-Aware**: Bağlam duyarlı yetkilendirme

### 7.2 Veri Güvenliği ve Gizlilik

#### 7.2.1 Veri Koruma Önlemleri
- **Encryption at Rest**: Veritabanında şifreleme
- **Encryption in Transit**: İletimde SSL/TLS
- **Data Masking**: Hassas veri maskeleme
- **Access Logging**: Erişim kayıtları

#### 7.2.2 Gizlilik Politikaları
- **GDPR Compliance**: Avrupa veri koruma uyumluluğu
- **Data Minimization**: Minimum veri toplama
- **Right to Erasure**: Silme hakkı desteği
- **Consent Management**: Onay yönetimi

### 7.3 Audit Trail ve İzleme

#### 7.3.1 Audit Logging
- **User Actions**: Kullanıcı eylemlerinin kaydı
- **Data Changes**: Veri değişikliklerinin izlenmesi
- **System Events**: Sistem olaylarının loglanması
- **Security Events**: Güvenlik olaylarının takibi

#### 7.3.2 İzleme ve Alerting
- **Real-time Monitoring**: Gerçek zamanlı izleme
- **Anomaly Detection**: Anormal davranış tespiti
- **Security Alerts**: Güvenlik uyarıları
- **Compliance Reporting**: Uyumluluk raporlaması

---

## Sonuç

Bu dokümantasyon, Personel Yetkinlik Değerlendirme Sisteminin kapsamlı teknik açıklamasını sunmaktadır. Sistem, modern yazılım mimarisi prensipleri kullanılarak geliştirilmiş, ölçeklenebilir, güvenli ve performanslı bir çözümdür.

Sistemin başarılı implementasyonu ve sürdürülmesi için bu dokümantasyonda belirtilen tüm bileşenlerin koordineli çalışması kritik önem taşımaktadır.
