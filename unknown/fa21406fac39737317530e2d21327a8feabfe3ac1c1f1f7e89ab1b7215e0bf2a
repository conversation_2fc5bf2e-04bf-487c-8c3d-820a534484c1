using AcademicPerformance.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Localization;
using AcademicPerformance.Consts;
using AcademicPerformance.Models.Dtos;
using AcademicPerformance.Controllers.Base;
using Rlx.Shared.Resources;
using Rlx.Shared.Interfaces;
namespace AcademicPerformance.Controllers
{
    /// <summary>
    /// Statik kriter verileri API controller'ı
    /// UI'dan akademisyen TC'si alır, katsayı + kriter değerlerini döndürür
    /// Puan hesaplaması raporlama sisteminde yapılacak
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class StaticCriterionDataController : BaseApiController
    {
        private readonly IStaticCriterionDataProvider _dataProvider;
        private readonly ILogger<StaticCriterionDataController> _logger;
        private readonly IRlxSystemLogHelper<StaticCriterionDataController> _systemLogHelper;
        public StaticCriterionDataController(
            IStaticCriterionDataProvider dataProvider,
            ILogger<StaticCriterionDataController> logger,
            IRlxSystemLogHelper<StaticCriterionDataController> systemLogHelper,
            IStringLocalizer<SharedResource> localizer) : base(localizer)
        {
            _dataProvider = dataProvider;
            _logger = logger;
            _systemLogHelper = systemLogHelper;
        }
        /// <summary>
        /// Akademisyen TC'sine göre tüm statik kriter verilerini getirir
        /// UI sadece TC gönderir, backend katsayı + kriter değerlerini döndürür
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <returns>Katsayı ve kriter değerleri (puan hesaplanmamış)</returns>
        [HttpGet("academician/{academicianTc}")]
        [Authorize(APConsts.Policies.ViewStaticData)]
        public async Task<IActionResult> GetAcademicianStaticCriteria(string academicianTc)
        {
            try
            {
                if (string.IsNullOrEmpty(academicianTc) || academicianTc.Length != 11)
                {
                    return BadRequestResponse(_localizer["InvalidTCIdentityNumber"].Value);
                }
                _logger.LogInformation("Getting static criteria data for academician: {AcademicianTc}", academicianTc);
                await _systemLogHelper.LogInfoAsync($"Getting static criteria data for academician: {academicianTc}");
                // Tüm ArelBridge statik kriterlerini getir
                var staticCriteriaData = await _dataProvider.GetAllArelBridgeStaticCriterionDataAsync(academicianTc);
                if (!staticCriteriaData.Any())
                {
                    return NotFoundResponse(_localizer["NoStaticCriteriaDataFoundForAcademician"].Value);
                }
                // Response DTO'ya dönüştür
                var response = new AcademicianStaticCriteriaResponseDto
                {
                    AcademicianTc = academicianTc,
                    DataRetrievalDate = DateTime.UtcNow,
                    TotalCriteriaCount = staticCriteriaData.Count,
                    StaticCriteria = staticCriteriaData.Select(data => new StaticCriterionResponseDto
                    {
                        CriterionId = data.StaticCriterionSystemId,
                        CriterionName = data.CriterionName,
                        RawValue = data.RawValue,
                        Coefficient = data.Coefficient,
                        DataType = data.DataType,
                        DataSource = data.DataSource,
                        LastUpdated = data.LastUpdated ?? data.CalculationDate,
                        IsFromCache = data.IsFromCache
                    }).ToList()
                };
                _logger.LogInformation("Successfully retrieved {Count} static criteria for academician: {AcademicianTc}",
                    response.TotalCriteriaCount, academicianTc);
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved {response.TotalCriteriaCount} static criteria for academician: {academicianTc}");
                return SuccessResponse(response, _localizer["StaticCriteriaDataRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting static criteria data for academician: {AcademicianTc}", academicianTc);
                await _systemLogHelper.LogErrorAsync($"Error getting static criteria data for academician: {academicianTc}", ex);
                return HandleException(ex, _localizer["ErrorRetrievingStaticCriteriaData"].Value);
            }
        }
        /// <summary>
        /// Belirli statik kriterler için veri getirir
        /// </summary>
        /// <param name="academicianTc">Akademisyen TC Kimlik Numarası</param>
        /// <param name="criterionIds">İstenen kriter ID'leri</param>
        /// <returns>Belirli kriterlerin katsayı ve değerleri</returns>
        [HttpPost("academician/{academicianTc}/specific")]
        [Authorize(APConsts.Policies.ViewStaticData)]
        public async Task<IActionResult> GetSpecificStaticCriteria(
            string academicianTc,
            [FromBody] List<string> criterionIds)
        {
            try
            {
                if (string.IsNullOrEmpty(academicianTc) || academicianTc.Length != 11)
                {
                    return BadRequestResponse(_localizer["InvalidTCIdentityNumber"].Value);
                }
                if (criterionIds == null || !criterionIds.Any())
                {
                    return BadRequestResponse(_localizer["AtLeastOneCriterionIdRequired"].Value);
                }
                _logger.LogInformation("Getting specific static criteria data for academician: {AcademicianTc}, Criteria: {CriterionIds}",
                    academicianTc, string.Join(", ", criterionIds));
                await _systemLogHelper.LogInfoAsync($"Getting specific static criteria data for academician: {academicianTc}, Criteria: {string.Join(", ", criterionIds)}");
                var staticCriteriaData = await _dataProvider.GetStaticCriterionDataAsync(academicianTc, criterionIds);
                if (!staticCriteriaData.Any())
                {
                    return NotFoundResponse(_localizer["NoDataFoundForSpecifiedCriteria"].Value);
                }
                var response = new AcademicianStaticCriteriaResponseDto
                {
                    AcademicianTc = academicianTc,
                    DataRetrievalDate = DateTime.UtcNow,
                    TotalCriteriaCount = staticCriteriaData.Count,
                    StaticCriteria = staticCriteriaData.Select(data => new StaticCriterionResponseDto
                    {
                        CriterionId = data.StaticCriterionSystemId,
                        CriterionName = data.CriterionName,
                        RawValue = data.RawValue,
                        Coefficient = data.Coefficient,
                        DataType = data.DataType,
                        DataSource = data.DataSource,
                        LastUpdated = data.LastUpdated ?? data.CalculationDate,
                        IsFromCache = data.IsFromCache
                    }).ToList()
                };
                await _systemLogHelper.LogInfoAsync($"Successfully retrieved {response.TotalCriteriaCount} specific static criteria for academician: {academicianTc}");
                return SuccessResponse(response, _localizer["SpecificStaticCriteriaDataRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting specific static criteria data for academician: {AcademicianTc}", academicianTc);
                await _systemLogHelper.LogErrorAsync($"Error getting specific static criteria data for academician: {academicianTc}", ex);
                return HandleException(ex, _localizer["ErrorRetrievingSpecificStaticCriteriaData"].Value);
            }
        }
        /// <summary>
        /// Toplu akademisyen verilerini getirir
        /// </summary>
        /// <param name="request">Toplu veri talep DTO'su</param>
        /// <returns>Birden fazla akademisyen için statik kriter verileri</returns>
        [HttpPost("batch")]
        [Authorize(APConsts.Policies.ViewStaticData)]
        public async Task<IActionResult> GetBatchStaticCriteria(
            [FromBody] BatchStaticCriteriaRequestDto request)
        {
            try
            {
                if (request.AcademicianTcs == null || !request.AcademicianTcs.Any())
                {
                    return BadRequestResponse(_localizer["AtLeastOneAcademicianTCRequired"].Value);
                }
                if (request.AcademicianTcs.Count > 100)
                {
                    return BadRequestResponse(_localizer["MaximumBatchSizeExceeded"].Value);
                }
                _logger.LogInformation("Getting batch static criteria data for {Count} academicians", request.AcademicianTcs.Count);
                await _systemLogHelper.LogInfoAsync($"Getting batch static criteria data for {request.AcademicianTcs.Count} academicians");
                var criterionIds = request.CriterionIds ?? new List<string>();
                var batchData = await _dataProvider.GetBatchStaticCriterionDataAsync(request.AcademicianTcs, criterionIds);
                var response = new BatchAcademicianStaticCriteriaResponseDto
                {
                    RequestedAcademicianCount = request.AcademicianTcs.Count,
                    ProcessedAcademicianCount = batchData.Count,
                    DataRetrievalDate = DateTime.UtcNow,
                    AcademicianData = batchData.Select(kvp => new AcademicianStaticCriteriaResponseDto
                    {
                        AcademicianTc = kvp.Key,
                        DataRetrievalDate = DateTime.UtcNow,
                        TotalCriteriaCount = kvp.Value.Count,
                        StaticCriteria = kvp.Value.Select(data => new StaticCriterionResponseDto
                        {
                            CriterionId = data.StaticCriterionSystemId,
                            CriterionName = data.CriterionName,
                            RawValue = data.RawValue,
                            Coefficient = data.Coefficient,
                            DataType = data.DataType,
                            DataSource = data.DataSource,
                            LastUpdated = data.LastUpdated ?? data.CalculationDate,
                            IsFromCache = data.IsFromCache
                        }).ToList()
                    }).ToList()
                };
                _logger.LogInformation("Successfully processed batch request for {ProcessedCount}/{RequestedCount} academicians",
                    response.ProcessedAcademicianCount, response.RequestedAcademicianCount);
                await _systemLogHelper.LogInfoAsync($"Successfully processed batch request for {response.ProcessedAcademicianCount}/{response.RequestedAcademicianCount} academicians");
                return SuccessResponse(response, _localizer["BatchStaticCriteriaDataRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting batch static criteria data");
                await _systemLogHelper.LogErrorAsync("Error getting batch static criteria data", ex);
                return HandleException(ex, _localizer["ErrorRetrievingBatchStaticCriteriaData"].Value);
            }
        }
        /// <summary>
        /// Statik kriter veri sağlayıcı performans metriklerini getirir
        /// </summary>
        /// <returns>Performans metrikleri</returns>
        [HttpGet("metrics")]
        [Authorize(APConsts.Policies.RequireAdminRole)]
        public async Task<IActionResult> GetPerformanceMetrics()
        {
            try
            {
                await _systemLogHelper.LogInfoAsync("Getting performance metrics");
                var metrics = await _dataProvider.GetPerformanceMetricsAsync();
                await _systemLogHelper.LogInfoAsync("Successfully retrieved performance metrics");
                return SuccessResponse(metrics, _localizer["PerformanceMetricsRetrievedSuccessfully"].Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance metrics");
                await _systemLogHelper.LogErrorAsync("Error getting performance metrics", ex);
                return HandleException(ex, _localizer["ErrorRetrievingPerformanceMetrics"].Value);
            }
        }

    }
}
